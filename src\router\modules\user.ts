const Layout = () => import("@/layout/index.vue");

export default {
  path: "/systems/users/management",
  name: "User",
  component: Layout,
  redirect: "/systems/users",
  meta: {
    icon: "ep:user",
    title: "User Management",
    rank: 3
  },
  children: [
    {
      path: "/systems/users",
      name: "UserIndex",
      component: () => import("@/views/system/user/index.vue"),
      meta: {
        title: "User Management",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
