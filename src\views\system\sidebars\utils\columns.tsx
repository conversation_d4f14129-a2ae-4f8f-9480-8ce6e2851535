import dayjs from "dayjs";
import { transformI18n } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { isAllEmpty } from "@pureadmin/utils";

// Helper function for date formatting
const formatDateTime = (date: string | null): string => {
  return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : "-";
};

// Status color mapping
const statusColors = {
  active: "success",
  inactive: "danger",
  draft: "warning",
  archived: "info"
} as const;

export const columns: TableColumnList = [
  {
    headerRenderer: () => transformI18n("Title"),
    prop: "title",
    align: "left",
    minWidth: 210,
    cellRenderer: ({ row }) => (
      <>
        <span class="inline-block mr-1">
          {h(useRenderIcon(row.icon), {
            style: { paddingTop: "1px" }
          })}
        </span>
        <span>{transformI18n(row.title)}</span>
      </>
    )
  },
  {
    prop: "path",
    align: "left",
    minWidth: 210,
    headerRenderer: () => transformI18n("Path"),
    showOverflowTooltip: true,
    formatter: ({ path }) => path || "-"
  },
  {
    prop: "menuType",
    align: "center",
    width: 100,
    headerRenderer: () => transformI18n("Type"),
    cellRenderer: ({ row }) => {
      const menuTypes = {
        0: { label: "Menu", type: "primary" },
        1: { label: "Iframe", type: "warning" },
        2: { label: "External", type: "danger" },
        3: { label: "Button", type: "info" }
      };

      const { label, type } = menuTypes[row.menuType] || menuTypes[0];

      return h(
        ElTag,
        {
          type,
          size: "small",
          effect: "plain"
        },
        () => transformI18n(label)
      );
    }
  },
  {
    headerRenderer: () => transformI18n("Order"),
    prop: "rank",
    align: "center",
    width: 100
  },
  {
    headerRenderer: () => transformI18n("Hidden"),
    prop: "showLink",
    align: "center",
    formatter: ({ showLink }) =>
      showLink ? transformI18n("No") : transformI18n("Yes"),
    width: 100
  },
  {
    label: "",
    fixed: "right",
    width: 160,
    slot: "operation",
    sortable: false,
    align: "center"
  }
];
