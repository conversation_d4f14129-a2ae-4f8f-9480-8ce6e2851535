import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/blog/post/utils/type";

/* ***************************
 * API Data Fetching
 *************************** */

export const getPosts = (params?: object) => {
  return http.request<Result>("get", "/api/auth/blog/posts", {
    params
  });
};

export const getPostById = (id: number) => {
  return http.request<Result>("get", `/api/auth/blog/posts/${id}`);
};

/* ***************************
 * API CRUD Operations
 *************************** */

export const createPost = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/blog/posts", {
    data: useConvertKeyToSnake(data)
  });
};

export const updatePostById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/blog/posts/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/* ***************************
 * Delete handlers and actions
 *************************** */

export const deletePost = (id: number) => {
  return http.request<Result>("delete", `/api/auth/blog/posts/${id}`);
};

export const bulkDeletePosts = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/blog/posts/bulk-delete", {
    data
  });
};

/* ***************************
 * Destroy handlers and actions
 *************************** */

export const destroyPost = (id: number) => {
  return http.request<Result>("delete", `/api/auth/blog/posts/${id}/force-delete`);
};

export const bulkDestroyPosts = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/blog/posts/bulk-destroy", {
    data
  });
};

/* ***************************
 * Restore handlers and actions
 *************************** */

export const restorePost = (id: number) => {
  return http.request<Result>("put", `/api/auth/blog/posts/${id}/restore`);
};

export const bulkRestorePosts = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/blog/posts/bulk-restore", {
    data
  });
};

/* ***************************
 * Dropdown/Select Options
 *************************** */

export const dropdownPosts = () => {
  return http.request<Result>("get", "/api/auth/blog/posts/dropdown");
};
