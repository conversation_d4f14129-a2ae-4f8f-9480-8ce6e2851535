<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Knowledge Base Module</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Knowledge Base Module Test</h1>
    
    <div class="info test-result">
        <h3>Test Instructions:</h3>
        <p>1. Make sure the development server is running on http://localhost:8850</p>
        <p>2. Click the buttons below to test different aspects of the Knowledge Base module</p>
        <p>3. Check the results and any console errors</p>
    </div>

    <div>
        <button onclick="testRouteAccess()">Test Route Access</button>
        <button onclick="testAPIEndpoint()">Test API Endpoint</button>
        <button onclick="testTranslations()">Test Translations</button>
        <button onclick="openKnowledgeBase()">Open Knowledge Base Module</button>
    </div>

    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function testRouteAccess() {
            addResult('Testing route access...', 'info');
            
            // Test if we can access the route
            fetch('http://localhost:8850/knowledge-base/index')
                .then(response => {
                    if (response.ok) {
                        addResult('✅ Route access successful - Knowledge Base route is accessible', 'success');
                    } else {
                        addResult(`❌ Route access failed - Status: ${response.status}`, 'error');
                    }
                })
                .catch(error => {
                    addResult(`❌ Route access error: ${error.message}`, 'error');
                });
        }

        function testAPIEndpoint() {
            addResult('Testing API endpoint...', 'info');
            
            // Test the API endpoint
            fetch('http://localhost:8850/api/auth/knowledge-bases')
                .then(response => {
                    if (response.ok) {
                        addResult('✅ API endpoint accessible', 'success');
                        return response.json();
                    } else {
                        addResult(`⚠️ API endpoint returned status: ${response.status} (This might be expected if backend is not running)`, 'info');
                    }
                })
                .then(data => {
                    if (data) {
                        addResult(`✅ API response received: ${JSON.stringify(data).substring(0, 100)}...`, 'success');
                    }
                })
                .catch(error => {
                    addResult(`⚠️ API endpoint error: ${error.message} (This is expected if backend is not running)`, 'info');
                });
        }

        function testTranslations() {
            addResult('Testing translations...', 'info');
            
            // Test if translation files are accessible
            fetch('http://localhost:8850/locales/en.json')
                .then(response => response.json())
                .then(data => {
                    if (data['Knowledge Base Management']) {
                        addResult('✅ English translations found for Knowledge Base Management', 'success');
                    } else {
                        addResult('❌ English translations missing for Knowledge Base Management', 'error');
                    }
                })
                .catch(error => {
                    addResult(`❌ Translation test error: ${error.message}`, 'error');
                });

            fetch('http://localhost:8850/locales/vi.json')
                .then(response => response.json())
                .then(data => {
                    if (data['Knowledge Base Management']) {
                        addResult('✅ Vietnamese translations found for Knowledge Base Management', 'success');
                    } else {
                        addResult('❌ Vietnamese translations missing for Knowledge Base Management', 'error');
                    }
                })
                .catch(error => {
                    addResult(`❌ Translation test error: ${error.message}`, 'error');
                });
        }

        function openKnowledgeBase() {
            addResult('Opening Knowledge Base module in new tab...', 'info');
            window.open('http://localhost:8850/knowledge-base/index', '_blank');
        }

        // Run initial tests
        window.onload = function() {
            addResult('Knowledge Base Module Test Started', 'info');
            addResult('Click the buttons above to run specific tests', 'info');
        };
    </script>
</body>
</html>
