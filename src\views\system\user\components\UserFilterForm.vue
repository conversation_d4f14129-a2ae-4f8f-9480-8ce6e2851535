<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Username")),
    prop: "username",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("First Name")),
    prop: "firstName",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Last Name")),
    prop: "lastName",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Email")),
    prop: "email",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Gender")),
    prop: "gender",
    valueType: "select",
    options: [
      { label: $t("Male"), value: "male" },
      { label: $t("Female"), value: "female" },
      { label: $t("Other"), value: "other" }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" },
      { label: $t("Suspended"), value: "suspended" },
      { label: $t("Banned"), value: "banned" },
      { label: $t("Pending"), value: "pending" }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Email Verified")),
    prop: "isVerified",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Verified"), value: true },
      { label: $t("Not Verified"), value: false }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Trash Status")),
    prop: "isTrashed",
    valueType: "select",
    options: [
      { label: $t("Normal"), value: "no" },
      { label: $t("Trashed"), value: "yes" }
    ],
    fieldProps: {
      placeholder: ""
    }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
};

const handleReset = () => {
  emit("reset");
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          {{ $t("Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
