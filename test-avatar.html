<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Avatar Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .avatar-preview {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #ddd;
        }
        .user-row {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user-email {
            color: #666;
            font-size: 14px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .avatar-fallback {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h1>User Avatar Test Preview</h1>
    <p>This shows how the User module will display avatars in the table:</p>
    
    <div class="user-row">
        <img src="https://via.placeholder.com/50/007bff/ffffff?text=JD" alt="John Doe" class="avatar-preview">
        <div class="user-info">
            <div class="user-name">John Doe</div>
            <div class="user-email"><EMAIL></div>
        </div>
        <span class="status-badge status-active">Active</span>
    </div>
    
    <div class="user-row">
        <div class="avatar-fallback">JS</div>
        <div class="user-info">
            <div class="user-name">Jane Smith</div>
            <div class="user-email"><EMAIL></div>
        </div>
        <span class="status-badge status-active">Active</span>
    </div>
    
    <div class="user-row">
        <img src="https://via.placeholder.com/50/28a745/ffffff?text=MB" alt="Mike Brown" class="avatar-preview">
        <div class="user-info">
            <div class="user-name">Mike Brown</div>
            <div class="user-email"><EMAIL></div>
        </div>
        <span class="status-badge status-inactive">Inactive</span>
    </div>
    
    <div class="user-row">
        <div class="avatar-fallback">AL</div>
        <div class="user-info">
            <div class="user-name">Alice Lee</div>
            <div class="user-email"><EMAIL></div>
        </div>
        <span class="status-badge status-active">Active</span>
    </div>

    <h2>Features Added:</h2>
    <ul>
        <li>✅ Avatar column in User table with 50px size</li>
        <li>✅ Fallback to first letter of name when no avatar</li>
        <li>✅ Avatar upload field in User form</li>
        <li>✅ Avatar filter option (Has Avatar / No Avatar)</li>
        <li>✅ Avatar upload API endpoint</li>
        <li>✅ Translation keys for avatar-related text</li>
        <li>✅ File validation (image types, 5MB limit)</li>
        <li>✅ Auto upload handling in form submission</li>
    </ul>

    <h2>Usage:</h2>
    <p>1. Avatar column shows user profile pictures or initials</p>
    <p>2. When creating/editing users, upload avatar in the form</p>
    <p>3. Filter users by avatar status in the filter form</p>
    <p>4. Supports JPG, PNG, JPEG images under 5MB</p>
</body>
</html>
