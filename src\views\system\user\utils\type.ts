export type FormItemProps = {
  id?: number | null;
  username?: string;
  password?: string;
  passwordConfirmation?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  avatar?: string;
  birthday?: string;
  gender?: string;
  phone?: string;
  address?: string;
  geoDivisionId?: number;
  countryId?: number;
  status?: string;
  isVerified?: boolean;
  newsletterSubscribed?: boolean;
  roles?: number[];
  createdAt?: string;
  updatedAt?: string;
};

export type UserFilterProps = {
  username?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  gender?: string;
  status?: string;
  isVerified?: boolean;
  roles?: number[];
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
