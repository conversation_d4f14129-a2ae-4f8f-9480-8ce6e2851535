<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import { computed, h, ref, watch } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  type FieldValues,
  OptionsType,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";

import {
  fixedTagOptions,
  frameLoadingOptions,
  hiddenTagOptions,
  keepAliveOptions,
  menuTypeOptions,
  showLinkOptions,
  showParentOptions
} from "../utils/enums";
import { IconSelect } from "@/components/ReIcon";
import { getSidebars } from "@/api/system/sidebar";
import { formatHigherMenuOptions, useConvertKeyToCamel } from "@/utils/helpers";
import { cloneDeep } from "@pureadmin/utils";
import { handleTree } from "@/utils/tree";
import { getPermissionGroups, getRoleOptions } from "@/api/role";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

// Thêm watch để theo dõi values
watch(
  () => props.values,
  newVal => {
    console.log("Form values changed:", newVal);
    console.log("Menu type:", newVal.menuType);
  },
  { deep: true, immediate: true }
);

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const formRef = ref();
const loading = ref(false);

const getSidebar = async () => {
  try {
    const { data } = await getSidebars();
    let newData = useConvertKeyToCamel(data);
    const dataList = handleTree(newData);
    emit("update:values", {
      ...props.values,
      higherMenuOptions: formatHigherMenuOptions(cloneDeep(dataList))
    });
  } catch (error) {
    console.error("Error fetching sidebars:", error);
  }
};

const getRoles = async () => {
  try {
    const { data } = await getRoleOptions();
    const roles = useConvertKeyToCamel(data).map((item: any) => ({
      label: item.label,
      value: item.key
    }));

    emit("update:values", {
      ...props.values,
      roleList: roles
    });
  } catch (error) {
    console.error("Error fetching roles:", error);
  }
};
const getPermissions = async () => {
  try {
    const { data } = await getPermissionGroups();
    emit("update:values", {
      ...props.values,
      permissionList: useConvertKeyToCamel(data).map((item: any) => ({
        label: item.name,
        value: item.displaName
      }))
    });
  } catch (error) {
    console.error("Error fetching roles:", error);
  }
};

watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      getSidebar();
      getRoles();
      getPermissions();
    }
  }
);

const formColumns: PlusColumn[] = [
  {
    label: computed(() => transformI18n("Menu Type")),
    prop: "menuType",
    valueType: "select",
    options: menuTypeOptions as OptionsType[],
    colProps: { span: 24 }
  },
  {
    label: computed(() => transformI18n("Roles")),
    prop: "roles",
    valueType: "select",
    fieldProps: {
      multiple: true
    },
    // @ts-ignore
    options: computed(() => props.values.roleList),
    colProps: { span: 24 }
  },
  {
    label: computed(() => transformI18n("Permission")),
    prop: "auths",
    valueType: "cascader",
    // @ts-ignore
    options: computed(() => props.values.permissionList),
    hideInForm: computed(() => Number(props.values.menuType) !== 3),
    colProps: { span: 24 }
  },
  {
    label: computed(() => transformI18n("Parent Menu")),
    prop: "parentId",
    valueType: "cascader",
    // @ts-ignore
    options: computed(() => props.values.higherMenuOptions),
    fieldProps: {
      props: {
        value: "id",
        label: "title",
        emitPath: false,
        checkStrictly: true
      },
      clearable: true,
      filterable: true
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Menu Name")),
    prop: "title",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: transformI18n("Please enter menu name"),
        trigger: ["blur", "change"]
      }
    ],
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Route Name")),
    prop: "name",
    valueType: "input",
    required: true,
    hideInForm: computed(() => props.values.menuType === 3),
    rules: [
      {
        required: true,
        message: transformI18n("Please enter route name"),
        trigger: ["blur", "change"]
      }
    ],
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Route Path")),
    prop: "path",
    valueType: "input",
    required: true,
    hideInForm: computed(() => props.values.menuType === 3),
    rules: [
      {
        required: true,
        message: transformI18n("Please enter route path"),
        trigger: ["blur", "change"]
      }
    ],
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Component Path")),
    prop: "component",
    valueType: "input",
    hideInForm: computed(() => props.values.menuType !== 0),
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Menu Order")),
    prop: "rank",
    valueType: "input-number",
    fieldProps: {
      min: 1,
      max: 9999,
      class: "!w-full"
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Route Redirect")),
    prop: "redirect",
    valueType: "input",
    hideInForm: computed(() => Number(props.values.menuType) !== 0),
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Menu Icon")),
    prop: "icon",
    valueType: "",
    hideInForm: computed(() => Number(props.values.menuType) === 3),
    renderField: () =>
      h(IconSelect, {
        // @ts-ignore
        modelValue: props?.values?.icon,
        "onUpdate:modelValue": val => {
          if (props?.values) {
            // eslint-disable-next-line vue/no-mutating-props
            props.values.icon = val;
          }
        },
        class: "w-full"
      }),
    fieldProps: { class: "w-full" },
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Extra Icon")),
    prop: "extraIcon",
    valueType: "input",
    hideInForm: computed(() => Number(props.values.menuType) === 3),
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Enter Animation")),
    prop: "enterTransition",
    valueType: "",
    hideInForm: computed(() => Number(props.values.menuType) >= 2),
    renderComponent: "ReAnimateSelector",
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Leave Animation")),
    prop: "leaveTransition",
    valueType: "",
    hideInForm: computed(() => Number(props.values.menuType) >= 2),
    renderComponent: "ReAnimateSelector",
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Active Menu")),
    prop: "activePath",
    valueType: "input",
    hideInForm: computed(() => Number(props.values.menuType) !== 0),
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Frame URL")),
    prop: "frameSrc",
    valueType: "input",
    hideInForm: computed(() => Number(props.values.menuType) !== 1),
    colProps: { span: 12 }
  },
  {
    label: computed(() => transformI18n("Frame Loading")),
    prop: "frameLoading",
    valueType: "plus-radio",
    hideInForm: computed(() => Number(props.values.menuType) !== 1),
    options: frameLoadingOptions as OptionsType[],
    colProps: { span: 6 }
  },
  {
    label: computed(() => transformI18n("Show Menu")),
    prop: "showLink",
    valueType: "plus-radio",
    hideInForm: computed(() => props.values.menuType === 3),
    options: showLinkOptions as OptionsType[],
    colProps: { span: 6 }
  },
  {
    label: computed(() => transformI18n("Show Parent")),
    prop: "showParent",
    valueType: "plus-radio",
    hideInForm: computed(() => props.values.menuType === 3),
    options: showParentOptions as OptionsType[],
    colProps: { span: 6 }
  },
  {
    label: computed(() => transformI18n("Keep Alive")),
    prop: "keepAlive",
    valueType: "plus-radio",
    hideInForm: computed(() => Number(props.values.menuType) >= 2),
    options: keepAliveOptions as OptionsType[],
    colProps: { span: 6 }
  },
  {
    label: computed(() => transformI18n("Hidden Tag")),
    prop: "hiddenTag",
    valueType: "plus-radio",
    hideInForm: computed(() => Number(props.values.menuType) >= 2),
    options: hiddenTagOptions as OptionsType[],
    colProps: { span: 6 }
  },
  {
    label: computed(() => transformI18n("Fixed Tag")),
    prop: "fixedTag",
    valueType: "plus-radio",
    hideInForm: computed(() => Number(props.values.menuType) >= 2),
    options: fixedTagOptions as OptionsType[],
    colProps: { span: 6 }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  await formRef.value.formInstance.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true;
        emit("submit", values);
      } finally {
        loading.value = false;
      }
    }
  });
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="65%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 20 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ transformI18n("Sidebar Information") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ transformI18n("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ transformI18n("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
