import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/bot/utils/type";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getBots = (params?: object) => {
  return http.request<Result>("get", "/api/auth/bots", {
    params
  });
};

export const getBotById = (uuid: string) => {
  return http.request<Result>("get", `/api/auth/bots/${uuid}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createBot = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/bots", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateBotById = (uuid: string, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/bots/${uuid}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Hard Delete Operations
 ***************************
 */
export const destroyBotById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/bots/${id}/force`);
};

export const bulkDestroyBots = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/bots/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Dropdown Operations
 ***************************
 */
export const getAiModelDropdown = () => {
  return http.request<Result>("get", `/api/model-ai/dropdown`);
};

export const getGeneralPrompts = (params?: object) => {
  return http.request<Result>("get", `/api/auth/bot-general-prompt`, {
    params
  });
};

/*
 ***************************
 *   Knowledge Base Operations
 ***************************
 */
export const getKnowledgeBaseFiles = (params?: object) => {
  return http.request<Result>("get", `/api/auth/knowledge-bases/files`, {
    params
  });
};

export const getRemoveFile = (data: object) => {
  return http.request<Result>(
    "delete",
    `/api/auth/knowledge-bases/files/remove`,
    {
      data
    }
  );
};
