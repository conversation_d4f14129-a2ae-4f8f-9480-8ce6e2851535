const Layout = () => import("@/layout/index.vue");

export default {
  path: "/knowledge-base",
  name: "KnowledgeBase",
  component: Layout,
  redirect: "/knowledge-base/index",
  meta: {
    icon: "ri:book-line",
    title: "Knowledge Base Management",
    rank: 8
  },
  children: [
    {
      path: "/knowledge-base/index",
      name: "KnowledgeBaseIndex",
      component: () => import("@/views/knowledge-base/index.vue"),
      meta: {
        title: "Knowledge Base Management",
        showLink: true,
        auths: ["knowledge-base.read"]
      }
    }
  ]
} satisfies RouteConfigsTable;
