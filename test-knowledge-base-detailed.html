<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Base Detailed Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        .test-stats {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>🧪 Knowledge Base Detailed Testing Suite</h1>
    
    <div class="test-stats">
        <div class="stat-item">
            <div class="stat-number" id="totalTests">0</div>
            <div class="stat-label">Total Tests</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="passedTests">0</div>
            <div class="stat-label">Passed</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="failedTests">0</div>
            <div class="stat-label">Failed</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="warningTests">0</div>
            <div class="stat-label">Warnings</div>
        </div>
    </div>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
    </div>

    <div class="test-section">
        <h2>🚀 Quick Actions</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="openKnowledgeBase()">Open Knowledge Base Module</button>
        <button onclick="clearResults()">Clear Results</button>
        <button onclick="exportResults()">Export Results</button>
    </div>

    <div class="test-grid">
        <div class="test-section">
            <h3>🌐 Route & Navigation Tests</h3>
            <button onclick="testRouteAccess()">Test Route Access</button>
            <button onclick="testNavigation()">Test Navigation</button>
            <button onclick="testBreadcrumbs()">Test Breadcrumbs</button>
        </div>

        <div class="test-section">
            <h3>🔌 API Integration Tests</h3>
            <button onclick="testAPIEndpoints()">Test API Endpoints</button>
            <button onclick="testPagination()">Test Pagination</button>
            <button onclick="testSorting()">Test Sorting</button>
            <button onclick="testFiltering()">Test Filtering</button>
        </div>

        <div class="test-section">
            <h3>🎨 UI Component Tests</h3>
            <button onclick="testTableComponents()">Test Table Components</button>
            <button onclick="testFormComponents()">Test Form Components</button>
            <button onclick="testDrawerComponents()">Test Drawer Components</button>
            <button onclick="testFilterComponents()">Test Filter Components</button>
        </div>

        <div class="test-section">
            <h3>🔐 Permission Tests</h3>
            <button onclick="testPermissions()">Test Permissions</button>
            <button onclick="testRoleBasedAccess()">Test Role-based Access</button>
            <button onclick="testActionPermissions()">Test Action Permissions</button>
        </div>

        <div class="test-section">
            <h3>🌍 Translation Tests</h3>
            <button onclick="testTranslations()">Test Translations</button>
            <button onclick="testLanguageSwitching()">Test Language Switching</button>
            <button onclick="testMissingTranslations()">Test Missing Translations</button>
        </div>

        <div class="test-section">
            <h3>⚠️ Error Handling Tests</h3>
            <button onclick="testErrorScenarios()">Test Error Scenarios</button>
            <button onclick="testValidation()">Test Validation</button>
            <button onclick="testNetworkErrors()">Test Network Errors</button>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Test Results</h2>
        <div id="results"></div>
    </div>

    <script>
        // Test statistics
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        // Base URL for the application
        const BASE_URL = 'http://localhost:8850';
        
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            document.getElementById('warningTests').textContent = testStats.warnings;
            
            const progress = testStats.total > 0 ? ((testStats.passed + testStats.warnings) / testStats.total) * 100 : 0;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function addResult(message, type = 'info', category = 'General') {
            testStats.total++;
            if (type === 'success') testStats.passed++;
            else if (type === 'error') testStats.failed++;
            else if (type === 'warning') testStats.warnings++;
            
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <strong>${new Date().toLocaleTimeString()}</strong> 
                [${category}] ${message}
            `;
            resultsDiv.appendChild(resultDiv);
            
            updateStats();
            
            // Auto scroll to bottom
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testStats = { total: 0, passed: 0, failed: 0, warnings: 0 };
            updateStats();
            addResult('Test results cleared', 'info', 'System');
        }

        function exportResults() {
            const results = document.getElementById('results').innerText;
            const blob = new Blob([results], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `knowledge-base-test-results-${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            addResult('Test results exported', 'success', 'System');
        }

        // Route & Navigation Tests
        function testRouteAccess() {
            addResult('Testing route access...', 'info', 'Route');
            
            fetch(`${BASE_URL}/knowledge-base/index`)
                .then(response => {
                    if (response.ok) {
                        addResult('✅ Knowledge Base route is accessible', 'success', 'Route');
                    } else {
                        addResult(`❌ Route access failed - Status: ${response.status}`, 'error', 'Route');
                    }
                })
                .catch(error => {
                    addResult(`❌ Route access error: ${error.message}`, 'error', 'Route');
                });
        }

        function testNavigation() {
            addResult('Testing navigation...', 'info', 'Navigation');
            // This would typically involve checking if navigation elements exist
            addResult('⚠️ Navigation test requires manual verification', 'warning', 'Navigation');
        }

        function testBreadcrumbs() {
            addResult('Testing breadcrumbs...', 'info', 'Navigation');
            addResult('⚠️ Breadcrumb test requires manual verification', 'warning', 'Navigation');
        }

        // API Integration Tests
        function testAPIEndpoints() {
            addResult('Testing API endpoints...', 'info', 'API');
            
            const endpoints = [
                '/api/auth/knowledge-bases',
                '/api/auth/knowledge-bases/1'
            ];
            
            endpoints.forEach(endpoint => {
                fetch(`${BASE_URL}${endpoint}`)
                    .then(response => {
                        if (response.status === 401) {
                            addResult(`⚠️ ${endpoint} requires authentication (expected)`, 'warning', 'API');
                        } else if (response.ok) {
                            addResult(`✅ ${endpoint} is accessible`, 'success', 'API');
                        } else {
                            addResult(`❌ ${endpoint} returned status: ${response.status}`, 'error', 'API');
                        }
                    })
                    .catch(error => {
                        addResult(`⚠️ ${endpoint} error: ${error.message} (backend may not be running)`, 'warning', 'API');
                    });
            });
        }

        function testPagination() {
            addResult('Testing pagination...', 'info', 'API');
            addResult('⚠️ Pagination test requires backend integration', 'warning', 'API');
        }

        function testSorting() {
            addResult('Testing sorting...', 'info', 'API');
            addResult('⚠️ Sorting test requires backend integration', 'warning', 'API');
        }

        function testFiltering() {
            addResult('Testing filtering...', 'info', 'API');
            addResult('⚠️ Filtering test requires backend integration', 'warning', 'API');
        }

        // Continue with other test functions...
        function openKnowledgeBase() {
            addResult('Opening Knowledge Base module in new tab...', 'info', 'Navigation');
            window.open(`${BASE_URL}/knowledge-base/index`, '_blank');
        }

        // Initialize
        window.onload = function() {
            addResult('Knowledge Base Detailed Testing Suite initialized', 'info', 'System');
            addResult('Click "Run All Tests" to start comprehensive testing', 'info', 'System');
        };
    </script>
</body>
</html>
