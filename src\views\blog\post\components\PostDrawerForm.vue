<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, h, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useLanguageStore } from "@/store/modules/language";
import TinyEditor from "@/components/TinyEditor/index.vue";
import { getCategories } from "@/api/blog";
import { ElIcon, ElMessage, ElUpload, UploadProps } from "element-plus";
import { formatToken, getToken } from "@/utils/auth";
import { Plus } from "@element-plus/icons-vue";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const formRef = ref();
const loading = ref(false);
const categories = ref([]);
const imageUrl = ref("");
const previewVisible = ref(false);
const previewImageUrl = ref("");

// Function to get storage URL
const STORAGE_URL = (path: string) => {
  // If path is already a full URL, return it
  if (path && (path.startsWith("http") || path.startsWith("/"))) {
    return path;
  }
  // If path starts with /, prepend the base URL
  if (path && !path.startsWith("data:image")) {
    return `${import.meta.env.VITE_API_BASE_URL}/storage/${path}`;
  }
  // Otherwise, return the path as is
  return path || "";
};

// Handle preview image
const handlePreview = () => {
  if (imageUrl.value || props.values.image) {
    // @ts-ignore
    previewImageUrl.value = imageUrl.value || STORAGE_URL(props.values.image);
    previewVisible.value = true;
  }
};

watch(
  () => props.visible,
  async newValue => {
    if (newValue) {
      try {
        // Load categories khi form mở
        const { data } = await getCategories({ limit: 100 });
        categories.value = data.records;
      } catch (error) {
        console.error("Error loading categories:", error);
      }
    }
  },
  { immediate: true }
);

const formColumns: PlusColumn[] = [
  {
    hasLabel: false,
    prop: "image",
    valueType: "img",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please upload an image"),
        trigger: ["blur", "change"]
      }
    ],
    renderField() {
      // Initialize imageUrl if value exists
      if (props.values.image && !imageUrl.value) {
        // @ts-ignore
        imageUrl.value = STORAGE_URL(props.values.image);
      }

      const handleImageSuccess: UploadProps["onSuccess"] = response => {
        if (response?.success && response?.data) {
          imageUrl.value = STORAGE_URL(`${response.data?.url}`);

          // Create a new object to trigger reactivity
          const newValues = { ...props.values, image: response.data };

          // Update form value
          emit("update:values", newValues);

          ElMessage.success($t("Upload successful"));

          // Reset the upload component to allow re-uploading the same file
          const uploadRef = document.querySelector(
            '.slide-image-uploader input[type="file"]'
          );
          if (uploadRef) {
            // @ts-ignore
            uploadRef.value = "";
          }
        } else {
          ElMessage.error(response?.message || $t("Upload failed"));
        }
      };

      const beforeImageUpload: UploadProps["beforeUpload"] = rawFile => {
        const allowedExtensions = ["jpeg", "jpg", "png", "gif", "bmp"];
        const extension = rawFile.name.split(".").pop().toLowerCase();

        if (!allowedExtensions.includes(extension)) {
          ElMessage.error(
            $t("Image must be in JPEG, PNG, GIF, or BMP format!")
          );
          return false;
        } else if (rawFile.size / 1024 / 1024 > 2) {
          ElMessage.error($t("Image size can not exceed 2MB!"));
          return false;
        }
        return true;
      };

      return h("div", { class: "image-upload-container flex w-full" }, [
        h(
          ElUpload,
          {
            class:
              "slide-image-uploader mx-auto min-w-[300px] bg-gray-100 text-center",
            action: `${import.meta.env.VITE_API_BASE_URL}/api/auth/media/upload`,
            showFileList: false,
            onSuccess: handleImageSuccess,
            beforeUpload: beforeImageUpload,
            disabled: props.disabled,
            headers: {
              Authorization: formatToken(getToken()?.accessToken || "")
            }
          },
          [
            props.values.image || imageUrl.value
              ? h("img", {
                  src: imageUrl.value,
                  class: "",
                  style: {
                    width: "100%",
                    height: "180px",
                    display: "block"
                  }
                })
              : h(
                  ElIcon,
                  {
                    class: "mx-auto",
                    style: {
                      fontSize: "28px",
                      color: "#8c939d",
                      width: "100%",
                      height: "180px",
                      textAlign: "center"
                    }
                  },
                  [h(Plus)]
                )
          ]
        )
      ]);
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Category")),
    prop: "categoryId",
    valueType: "select",
    required: true,
    fieldProps: {
      placeholder: $t("Select category"),
      clearable: false,
      disabled: props.disabled,
      onChange: (value: any) => {
        console.log("Selected category:", value);
        console.log("Available categories:", categories.value);
      }
    },
    rules: [
      {
        required: true,
        message: $t("Please select category"),
        trigger: ["blur", "change"]
      }
    ],
    options: computed(
      () =>
        categories.value?.map(category => ({
          label: category.name,
          value: category.id
        })) || []
    ),
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Title")),
    prop: "title",
    valueType: "input",
    required: true,
    fieldProps: {
      placeholder: "",
      disabled: props.disabled
    },
    rules: [
      {
        required: true,
        message: $t("Please enter title"),
        trigger: ["blur", "change"]
      }
    ],
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    fieldProps: {
      placeholder: "",
      clearable: false,
      disabled: props.disabled
    },
    options: [
      { label: $t("Draft"), value: "draft" },
      { label: $t("Published"), value: "published" },
      { label: $t("Disabled"), value: "disabled" }
    ],
    colProps: { span: 8 }
  },
  {
    label: computed(() => $t("Published at")),
    prop: "publishedAt",
    valueType: "date-picker",
    fieldProps: {
      placeholder: "",
      type: "datetime",
      format: "YYYY-MM-DD HH:mm",
      valueFormat: "YYYY-MM-DD HH:mm",
      disabled: props.disabled
    },
    colProps: { span: 8 }
  },
  {
    label: computed(() => $t("Featured")),
    prop: "featured",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false,
      disabled: props.disabled
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    colProps: { span: 8 }
  },
  {
    label: computed(() => $t("Excerpt")),
    prop: "excerpt",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 3,
      maxlength: 300,
      showWordLimit: true,
      disabled: props.disabled
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Content")),
    prop: "content",
    valueType: "textarea",
    required: true,
    fieldProps: {
      placeholder: "",
      disabled: props.disabled
    },
    rules: [
      {
        required: true,
        message: $t("Please enter content"),
        trigger: ["blur", "change"]
      }
    ],
    renderField: (value: string, onChange: (value: string) => void) => {
      return h(TinyEditor, {
        modelValue: value,
        "onUpdate:modelValue": (newValue: string) => {
          onChange(newValue);
        },
        height: 250,
        style: { width: "100%" },
        disabled: props.disabled
      });
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Meta title")),
    prop: "metaTitle",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      disabled: props.disabled
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Meta description")),
    prop: "metaDescription",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 3,
      maxlength: 160,
      showWordLimit: true,
      disabled: props.disabled
    },
    colProps: { span: 24 }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;

  await formRef.value.formInstance.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true;
        const locale = useLanguageStore().currentLocale;

        // Prepare translations data
        values.translations = {
          [locale]: {
            title: values.title,
            slug: values.slug,
            content: values.content,
            metaTitle: values.metaTitle,
            metaDescription: values.metaDescription,
            excerpt: values.excerpt
          }
        };

        emit("submit", values);
      } finally {
        loading.value = false;
      }
    }
  });
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="70%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 16 },
      disabled: disabled
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Post Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :disabled="disabled"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss">
.tiny-editor-container {
  .el-drawer__body {
    overflow: visible;
  }

  .tox-tinymce-aux {
    z-index: 9999;
  }
}
</style>
