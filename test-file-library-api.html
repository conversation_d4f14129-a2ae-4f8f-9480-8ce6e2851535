<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test File Library API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .loading { opacity: 0.6; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        .file-item {
            padding: 8px;
            margin: 4px 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test File Library API</h1>
        <p>This page tests the new Knowledge Base Files API endpoint for the Bot Dialog Form.</p>
        
        <div class="test-section">
            <h3>API Configuration</h3>
            <p><strong>Endpoint:</strong> <code>/api/auth/knowledge-bases/files</code></p>
            <p><strong>Method:</strong> GET</p>
            <p><strong>Authentication:</strong> Required (Bearer Token)</p>
        </div>

        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testAPI()" id="testBtn">🚀 Test API Endpoint</button>
            <button onclick="clearResults()">🧹 Clear Results</button>
            <button onclick="testWithAuth()" id="authBtn">🔐 Test with Mock Auth</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>Expected Response Format</h3>
            <pre>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "document.pdf",
      "fileName": "document.pdf",
      "type": "PDF",
      "fileType": "PDF",
      "extension": "pdf",
      "size": "2.5 MB",
      "fileSize": "2.5 MB",
      "createdAt": "2024-01-15T10:30:00Z",
      "uploadedAt": "2024-01-15T10:30:00Z"
    }
  ]
}</pre>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8850';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testAPI() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '🔄 Testing...';
            
            addResult('Testing Knowledge Base Files API...', 'info');
            
            try {
                const response = await fetch(`${BASE_URL}/api/auth/knowledge-bases/files`);
                
                if (response.status === 401) {
                    addResult('⚠️ Authentication required (expected for protected endpoint)', 'warning');
                    addResult('Response status: 401 Unauthorized', 'warning');
                } else if (response.ok) {
                    const data = await response.json();
                    addResult('✅ API endpoint accessible', 'success');
                    addResult(`Response received: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success && Array.isArray(data.data)) {
                        addResult(`✅ Found ${data.data.length} files in library`, 'success');
                        displayFiles(data.data);
                    }
                } else {
                    addResult(`❌ API returned status: ${response.status}`, 'error');
                    const text = await response.text();
                    addResult(`Response: ${text}`, 'error');
                }
            } catch (error) {
                addResult(`⚠️ Network error: ${error.message}`, 'warning');
                addResult('This is expected if the backend is not running', 'info');
            }
            
            btn.disabled = false;
            btn.textContent = '🚀 Test API Endpoint';
        }

        async function testWithAuth() {
            const btn = document.getElementById('authBtn');
            btn.disabled = true;
            btn.textContent = '🔄 Testing with Auth...';
            
            addResult('Testing with mock authentication...', 'info');
            
            try {
                const response = await fetch(`${BASE_URL}/api/auth/knowledge-bases/files`, {
                    headers: {
                        'Authorization': 'Bearer mock-token-for-testing',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.status === 401) {
                    addResult('⚠️ Mock token rejected (expected)', 'warning');
                } else if (response.ok) {
                    const data = await response.json();
                    addResult('✅ API accessible with auth', 'success');
                    addResult(`Response: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`❌ API returned status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`⚠️ Network error: ${error.message}`, 'warning');
            }
            
            btn.disabled = false;
            btn.textContent = '🔐 Test with Mock Auth';
        }

        function displayFiles(files) {
            if (!files || files.length === 0) {
                addResult('📁 No files found in the library', 'info');
                return;
            }
            
            const filesHtml = files.map(file => `
                <div class="file-item">
                    <strong>${file.name || file.fileName || 'Unknown'}</strong><br>
                    Type: ${file.type || file.fileType || file.extension || 'Unknown'}<br>
                    Size: ${file.size || file.fileSize || 'Unknown'}<br>
                    Created: ${file.createdAt || file.uploadedAt || 'Unknown'}
                </div>
            `).join('');
            
            addResult(`📁 Files in library:<br>${filesHtml}`, 'success');
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            addResult('🎯 Page loaded. Ready to test File Library API', 'info');
            addResult('💡 Click "Test API Endpoint" to check if the backend is running', 'info');
        });
    </script>
</body>
</html>
