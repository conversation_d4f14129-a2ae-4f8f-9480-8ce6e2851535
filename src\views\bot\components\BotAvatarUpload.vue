<script setup lang="ts">
import { Plus } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";

interface Props {
  logoUrl?: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "change", file: any): void;
}>();

const handleAvatarChange = (file: any) => {
  emit("change", file);
};
</script>

<template>
  <div class="card">
    <h2 class="section-title !block text-center">{{ $t("Avatar") }}</h2>
    <div class="flex justify-center">
      <el-upload
        class="avatar-uploader"
        action="#"
        :show-file-list="false"
        :auto-upload="false"
        @change="handleAvatarChange"
      >
        <img
          v-if="props.logoUrl"
          :src="props.logoUrl"
          class="avatar"
          alt="avatar"
        />
        <el-icon v-else class="avatar-uploader-icon">
          <Plus />
        </el-icon>
      </el-upload>
    </div>
    <p class="text-xs text-center text-gray-500 !mt-4">
      {{ $t("Upload JPG, PNG, JPEG images. Size under 5MB.") }}
    </p>
  </div>
</template>

<style lang="scss" scoped>
.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
    object-fit: cover;
  }
}
</style>
