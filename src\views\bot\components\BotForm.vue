<script setup lang="ts">
import {
  ElMessageBox,
  ElMessage,
  ElMessageBox as ElMsgBox
} from "element-plus";
import type { FormInstance } from "element-plus";
import { reactive, ref, onMounted, onBeforeUnmount, computed } from "vue";
import {
  Promotion,
  UploadFilled,
  Document,
  Delete,
  Refresh,
  Search
} from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";

import { message } from "@/utils/message";
import { useRoute } from "vue-router";
import { getToken } from "@/utils/auth";
import {
  getAiModelDropdown,
  getGeneralPrompts,
  getKnowledgeBaseFiles,
  getRemoveFile
} from "@/views/bot/utils/auth-api";
import { botRules } from "../utils/rule";

// Import separate components
import BotAvatarUpload from "./BotAvatarUpload.vue";
import BotAdvancedSettings from "./BotAdvancedSettings.vue";
import BotBasicInfo from "./BotBasicInfo.vue";
import BotAIBrain from "./BotAIBrain.vue";
import BotChatInterface from "./BotChatInterface.vue";
import BotKnowledgeBase from "./BotKnowledgeBase.vue";

// --- State Management ---
interface Props {
  useBot: any;
}

const props = defineProps<{
  useBot: any;
}>();

const emit = defineEmits<{
  (e: "close"): void;
}>();
const formRef = ref<FormInstance>();
const aiModels = ref([]);

const loading = reactive({
  prompt: false,
  greeting: false,
  starters: false,
  submit: false,
  library: false
});

// File upload variables for BotKnowledgeBase
const fileLibrary = ref([]);
const selectedFiles = ref<string[]>([]);

const {
  drawerValues,
  handleSubmit: originalHandleSubmit,
  botFormRef,
  handleReset
} = props.useBot;

// Wrapper for handleSubmit to emit close after success
const handleSubmit = async (
  values: any,
  knowledgeFiles: any,
  clearUploadCallback?: () => void
) => {
  // Validate form first (like DrawerForm)
  if (!botFormRef.value) return false;

  try {
    const valid = await botFormRef.value.validate();
    if (!valid) return false;
  } catch (error) {
    console.error("Form validation failed:", error);
    return false;
  }

  try {
    loading.submit = true;
    const result = await originalHandleSubmit(
      values,
      knowledgeFiles,
      clearUploadCallback
    );
    if (result) {
      // Delay to show success state before closing (like DrawerForm)
      setTimeout(() => {
        emit("close");
        loading.submit = false;
      }, 1000);
    } else {
      loading.submit = false;
    }
    return result;
  } catch (error) {
    console.error("Submit error:", error);
    loading.submit = false;
    return false;
  }
};

// --- API Call Logic ---
const callGeneralPrompts = async (params: object) => {
  try {
    const { data, success } = await getGeneralPrompts(params);

    if (success) {
      return data;
    }
    return null;
  } catch (error) {
    return null;
  }
};

// --- Component Methods ---
const handleAvatarChange = (uploadFile: any) => {
  drawerValues.value.logoUrl = URL.createObjectURL(uploadFile.raw);
  drawerValues.value.logo = uploadFile.raw;
};

/*:::::::::::::::: General Prompt -----------------------------------------*/
const generatePrompt = async () => {
  try {
    const { value } = await ElMessageBox.prompt(
      $t("Briefly describe the role of the Agent:"),
      $t("✨ Prompt Generator Assistant"),
      {
        confirmButtonText: $t("Generate"),
        cancelButtonText: $t("Cancel"),
        inputPlaceholder: $t(
          "Example: Vietnamese literature lesson planning assistant"
        )
      }
    );

    if (!value) return;

    loading.prompt = true;
    const result = await callGeneralPrompts({
      type: "system_prompt",
      role: value
    });

    if (result) {
      drawerValues.value.systemPrompt = result.trim();
      message($t("Prompt generated successfully!"), { type: "success" });
      await formRef.value?.validateField("systemPrompt");
    }
  } catch (e) {
    if (e !== "cancel") {
      console.error("Error generating prompt:", e);
    }
  } finally {
    loading.prompt = false;
  }
};

const generateGreeting = async () => {
  if (!drawerValues.value.name) {
    message($t("Please enter AI Assistant Name first."), { type: "warning" });
    return;
  }

  loading.greeting = true;

  try {
    const result = await callGeneralPrompts({
      type: "greeting_message",
      name: drawerValues.value.name
    });
    if (result) {
      drawerValues.value.greetingMessage = result.replace(/"/g, "").trim();
    }
  } catch (e) {
    console.error("Error generating greeting:", e);
  } finally {
    loading.greeting = false;
  }
};

const generateStarters = async () => {
  if (!drawerValues.value.systemPrompt) {
    message($t("Please create System Prompt for best suggestions."), {
      type: "warning"
    });
    return;
  }

  loading.starters = true;
  try {
    const result = await callGeneralPrompts({
      type: "starting_message",
      system_prompt: drawerValues.value.systemPrompt
    });
    if (result) {
      try {
        const cleanedResult = result.match(/\[.*\]/s)?.[0] || result;
        const starters = JSON.parse(cleanedResult);
        if (
          Array.isArray(starters) &&
          starters.every(s => typeof s === "string")
        ) {
          drawerValues.value.starterMessages = starters.slice(0, 4);
        } else {
          message($t("AI returned data not in string array format."), {
            type: "error"
          });
        }
      } catch (e) {
        console.error("JSON parsing error:", e, "Raw result:", result);
        message($t("Cannot parse suggestions from AI."), { type: "error" });
      }
    }
  } catch (e) {
    console.error("Error generating starters:", e);
  } finally {
    loading.starters = false;
  }
};

const handleGetAiModelDropdown = async () => {
  try {
    const { data, success } = await getAiModelDropdown();
    if (success) {
      aiModels.value = data.map((model: any) => ({
        value: model.value,
        label: `${model.label} (${model.value})`
      }));
    }
  } catch (error) {
    console.error("Error loading AI models:", error);
  }
};

// Reset form function (like DrawerForm)
const resetForm = () => {
  if (botFormRef.value) {
    botFormRef.value.resetFields();
  }
};

// Expose resetForm for parent component
defineExpose({
  resetForm
});

// File upload functions - simplified for BotKnowledgeBase component
const filteredFileLibrary = computed(() => fileLibrary.value);

const handleRefreshLibrary = async () => {
  try {
    loading.library = true;
    const response = await getKnowledgeBaseFiles();
    if (response.success) {
      fileLibrary.value = response.data;
    }
  } catch (error) {
    console.error("Error refreshing file library:", error);
  } finally {
    loading.library = false;
  }
};

const handleDeleteFile = async (fileId: string) => {
  try {
    await ElMsgBox.confirm($t("Are you sure to delete this file?"));
    const response = await getRemoveFile({ id: fileId });
    if (response.success) {
      ElMessage.success($t("File deleted successfully"));
      // Reload file library after deletion
      await handleRefreshLibrary();
    }
  } catch (error) {
    console.error("Error deleting file:", error);
  }
};

onMounted(async () => {
  await handleGetAiModelDropdown();
  // Load initial file library
  try {
    const response = await getKnowledgeBaseFiles();
    if (response.success) {
      fileLibrary.value = response.data;
    }
  } catch (error) {
    console.error("Error loading file library:", error);
  }
});

onBeforeUnmount(() => {
  console.log("onBeforeUnmount----->:::", botFormRef.value);
  handleReset();
});
</script>

<template>
  <div class="w-full h-full">
    <div class="main-grid">
      <!-- Left Column -->
      <div class="left-column space-y-6">
        <BotAvatarUpload
          :logo-url="drawerValues.logoUrl"
          @change="handleAvatarChange"
        />

        <BotAdvancedSettings
          :knowledge-enabled="drawerValues.knowledge.enabled"
          @update:knowledge-enabled="
            val => (drawerValues.knowledge.enabled = val)
          "
        />
      </div>

      <!-- Right Column -->
      <div class="right-column space-y-6">
        <el-form
          ref="botFormRef"
          :model="drawerValues"
          :rules="botRules"
          label-position="top"
          require-asterisk-position="right"
          size="default"
          class="flex flex-col gap-2"
        >
          <BotBasicInfo
            :values="drawerValues"
            :ai-models="aiModels"
            @update:values="val => Object.assign(drawerValues, val)"
          />

          <BotAIBrain
            :system-prompt="drawerValues.systemPrompt"
            :loading="loading.prompt"
            @update:system-prompt="val => (drawerValues.systemPrompt = val)"
            @generate-prompt="generatePrompt"
          />

          <BotChatInterface
            :greeting-message="drawerValues.greetingMessage"
            :starter-messages="drawerValues.starterMessages"
            :name="drawerValues.name"
            :logo-url="drawerValues.logoUrl"
            :loading-greeting="loading.greeting"
            :loading-starters="loading.starters"
            @update:greeting-message="
              val => (drawerValues.greetingMessage = val)
            "
            @update:starter-messages="
              val => (drawerValues.starterMessages = val)
            "
            @generate-greeting="generateGreeting"
            @generate-starters="generateStarters"
          />

          <BotKnowledgeBase
            :drawer-values="drawerValues"
            :loading="loading.library"
            :file-library="filteredFileLibrary"
            :selected-files="selectedFiles"
            @update:file-library="val => (fileLibrary = val)"
            @update:selected-files="val => (selectedFiles = val)"
            @update:knowledge-text="val => (drawerValues.knowledge.text = val)"
            @update:new-uploads="val => (drawerValues.knowledge.newUploads = val)"
            @update:library-files="val => (drawerValues.knowledge.libraryFiles = val)"
            @update:bot-files="val => (drawerValues.knowledge.botFiles = val)"
            @refresh-library="handleRefreshLibrary"
            @delete-file="handleDeleteFile"
          />

          <div class="flex justify-end gap-2">
            <el-button
              round
              type="danger"
              :icon="Promotion"
              size="large"
              :loading="loading.submit"
              @click="handleSubmit(drawerValues, [], () => {})"
            >
              {{ $t("Save") }}
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.main-grid {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 2rem;
}

.left-column {
  position: sticky;
  top: 2rem;
  height: calc(100vh - 4rem);
}

.card {
  background-color: #ffffff;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.05),
    0 2px 4px -2px rgb(0 0 0 / 0.05);
  border: 1px solid #e2e8f0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-uploader .el-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.avatar {
  width: 150px;
  height: 150px;
  display: block;
  object-fit: cover;
}

.gemini-button {
  background: linear-gradient(to right, #8e44ad, #3498db);
  color: white;
  border: none;
}

.gemini-button:hover {
  opacity: 0.9;
}

.chat-preview-container {
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-preview-window {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.chat-preview-header {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
}

.chat-preview-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 0.75rem;
}

.chat-preview-body {
  padding: 1rem;
  flex-grow: 1;
  font-size: 0.875rem;
}

.chat-bubble {
  background-color: #e2e8f0;
  color: #334155;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem;
  border-bottom-left-radius: 0.25rem;
  max-width: 90%;
  display: inline-block;
  word-wrap: break-word;
}

.starter-pills-container {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.starter-pill {
  background-color: transparent;
  border: 1px solid #cbd5e1;
  color: #475569;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.2s;
}

.starter-pill:hover {
  background-color: #e2e8f0;
  border-color: #94a3b8;
}

@media (max-width: 1024px) {
  .main-grid {
    grid-template-columns: 1fr;
  }

  .left-column {
    position: static;
    height: auto;
  }
}
</style>
