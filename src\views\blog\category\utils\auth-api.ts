import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/blog/category/utils/type";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getCategories = (params?: object) => {
  return http.request<Result>("get", "/api/auth/blog/categories", {
    params
  });
};

export const getCategoryById = (id: number) => {
  return http.request<Result>("get", `/api/auth/blog/categories/${id}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createCategory = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/blog/categories", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateCategoryById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/blog/categories/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteCategory = (id: number) => {
  return http.request<Result>("delete", `/api/auth/blog/categories/${id}`);
};

export const bulkDeleteCategories = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/blog/categories/bulk-delete", {
    data
  });
};

/*
 ***************************
 *   Hard Delete Operations
 ***************************
 */
export const destroyCategory = (id: number) => {
  return http.request<Result>("delete", `/api/auth/blog/categories/${id}/force-delete`);
};

export const bulkDestroyCategories = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/blog/categories/bulk-destroy", {
    data
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreCategory = (id: number) => {
  return http.request<Result>("put", `/api/auth/blog/categories/${id}/restore`);
};

export const bulkRestoreCategories = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/blog/categories/bulk-restore", {
    data
  });
};

/*
 ***************************
 *   Dropdown Operations
 ***************************
 */
export const dropdownCategories = () => {
  return http.request<Result>("get", "/api/auth/blog/categories/dropdown");
};
