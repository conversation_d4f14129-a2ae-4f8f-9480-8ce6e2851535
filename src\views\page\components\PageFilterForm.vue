<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Page Title")),
    prop: "title",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Draft"), value: "draft" },
      { label: $t("Published"), value: "published" },
      { label: $t("Disabled"), value: "disabled" }
    ]
  },
  {
    label: computed(() => $t("Created Date")),
    prop: "createdAt",
    valueType: "date-picker",
    fieldProps: {
      type: "daterange",
      startPlaceholder: "",
      endPlaceholder: "",
      valueFormat: "YYYY-MM-DD",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Published date")),
    prop: "publishedAt",
    valueType: "date-picker",
    fieldProps: {
      type: "daterange",
      startPlaceholder: "",
      endPlaceholder: "",
      valueFormat: "YYYY-MM-DD",
      clearable: true
    }
  },
  {
    label: computed(() => $t("Trash status")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("In trash"), value: "yes" },
      { label: $t("Not in trash"), value: "no" }
    ]
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    const submittedValues = { ...values };

    // Handle created date range
    // @ts-ignore
    if (submittedValues.createdAt?.length === 2) {
      // @ts-ignore
      const [from, to] = submittedValues.createdAt;
      submittedValues.created_at_from = from;
      submittedValues.created_at_to = to;
    }

    // Handle updated date range
    // @ts-ignore
    if (submittedValues.publishedAt?.length === 2) {
      const [from, to] = submittedValues.publishedAt;
      submittedValues.published_at_from = from;
      submittedValues.published_at_to = to;
    }

    emit("submit", submittedValues);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  resetForm();
  emit("reset");
  emit("update:values", { isTrashed: "no" });
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="35%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Filter Pages") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:filter-3-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss" scoped>
.custom-group-header {
  @apply flex items-center justify-between;
}

.custom-footer {
  @apply flex items-center justify-end gap-2 mt-4;
}
</style>
