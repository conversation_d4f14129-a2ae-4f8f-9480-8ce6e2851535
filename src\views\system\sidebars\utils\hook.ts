import { reactive, ref, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel } from "@/utils/helpers";
import type { SidebarFilterProps } from "./type";
import {
  getSidebars,
  createSidebar,
  updateSidebarById,
  deleteSidebar
} from "./auth-api";

export function useSidebarHook() {
  /* ***************************
   * Data/State Management
   *************************** */
  const loading = ref(false);
  let filterRef = reactive<SidebarFilterProps>({ isTrashed: false });
  const multipleSelection = ref([]);
  const records = ref([]);
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({});
  const sidebarFormRef = ref();

  /* ***************************
   * API Data Fetching
   *************************** */
  const fnGetSidebars = async () => {
    loading.value = true;
    try {
      const res = await getSidebars(filterRef);
      records.value = useConvertKeyToCamel(res.data);
    } catch (error) {
      console.error("Error fetching sidebars:", error);
      message($t("Failed to fetch data"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * API CRUD Operations
   *************************** */
  const fnHandleCreateSidebar = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createSidebar(formData);
      if (response.success) {
        message(response.message, { type: "success" });
        await fnGetSidebars();
        return true;
      }
      message(response.message || $t("Create failed"), { type: "error" });
      return false;
    } catch (error) {
      console.error("Create sidebar error:", error);
      message($t("Create failed"), { type: "error" });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateSidebar = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateSidebarById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), { type: "success" });
        await fnGetSidebars();
        return true;
      }
      message(response.message || $t("Update failed"), { type: "error" });
      return false;
    } catch (error) {
      console.error("Update sidebar error:", error);
      message($t("Update failed"), { type: "error" });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Table Event Handlers
   *************************** */
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  /* ***************************
   * Delete handlers and actions
   *************************** */
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      loading.value = true;
      const response = await deleteSidebar(row.id);
      if (response.success) {
        message(response.message || $t("Delete successful"), { type: "success" });
        await fnGetSidebars();
      } else {
        message(response.message || $t("Delete failed"), { type: "error" });
      }
    } catch (error) {
      console.error("Delete sidebar error:", error);
      message($t("Delete failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Form Handlers
   *************************** */
  const handleSubmit = async (values: FieldValues) => {
    if (values.id != null) {
      await fnHandleUpdateSidebar(Number(values.id), values);
      return;
    }
    const success = await fnHandleCreateSidebar(values);
    if (success) {
      drawerValues.value = {};
      sidebarFormRef.value?.resetForm();
    }
  };

  const handleFilter = async (values: SidebarFilterProps) => {
    filterRef = values;
    await fnGetSidebars();
  };

  /* ***************************
   * Lifecycle
   *************************** */
  onMounted(() => {
    fnGetSidebars();
  });

  /* ***************************
   * Return Hook Interface
   *************************** */
  return {
    loading,
    filterRef,
    records,
    multipleSelection,
    filterVisible,
    drawerVisible,
    drawerValues,
    sidebarFormRef,
    fnGetSidebars,
    fnHandleCreateSidebar,
    fnHandleUpdateSidebar,
    fnHandleSelectionChange,
    handleDelete,
    handleSubmit,
    handleFilter
  };
}
