<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { usePostHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

const PostDrawerForm = defineAsyncComponent(
  () => import("./components/PostDrawerForm.vue")
);

const PostFilterForm = defineAsyncComponent(
  () => import("./components/PostFilterForm.vue")
);

const tableRef = ref();

const {
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  handleBulkDelete,
  handleDelete,
  handleBulkDestroy,
  handleBulkRestore,
  fnGetPosts,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  filterVisible,
  drawerVisible,
  drawerValues,
  postFormRef,
  handleSubmit,
  handleFilter,
  handleEdit
} = usePostHook();

onMounted(() => {
  fnGetPosts();
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        border
        :title="$t('Post Management')"
        :columns="columns"
        @refresh="fnGetPosts"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-button
            v-if="hasAuth('create-posts')"
            type="primary"
            :icon="IconifyIconOnline({ icon: 'ri:add-line' })"
            @click="drawerVisible = true"
          >
            {{ $t("Create") }}
          </el-button>
          <el-button
            v-if="hasAuth('delete-posts') && multipleSelection.length > 0"
            type="danger"
            :icon="IconifyIconOnline({ icon: 'ri:delete-bin-line' })"
            @click="handleBulkDelete"
          >
            {{ $t("Delete") }}
          </el-button>
          <el-button
            v-if="hasAuth('restore-posts') && multipleSelection.length > 0 && filterRef.isTrashed === 'yes'"
            type="success"
            :icon="IconifyIconOnline({ icon: 'ri:refresh-line' })"
            @click="handleBulkRestore"
          >
            {{ $t("Restore") }}
          </el-button>
          <el-button
            v-if="hasAuth('destroy-posts') && multipleSelection.length > 0 && filterRef.isTrashed === 'yes'"
            type="danger"
            :icon="IconifyIconOnline({ icon: 'ri:delete-bin-2-line' })"
            @click="handleBulkDestroy"
          >
            {{ $t("Destroy") }}
          </el-button>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            border
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown trigger="click">
                <el-button type="primary" size="small" text>
                  {{ $t("Operations") }}
                  <IconifyIconOnline
                    :icon="'ri:arrow-down-s-line'"
                    class="ml-1"
                  />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-if="hasAuth('edit-posts')"
                      @click="handleEdit(row)"
                    >
                      <IconifyIconOnline
                        :icon="'ri:edit-line'"
                        class="mr-2"
                      />
                      {{ $t("Edit") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="hasAuth('delete-posts') && filterRef.isTrashed === 'no'"
                      @click="handleDelete(row)"
                    >
                      <IconifyIconOnline
                        :icon="'ri:delete-bin-line'"
                        class="mr-2"
                      />
                      {{ $t("Delete") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="hasAuth('restore-posts') && filterRef.isTrashed === 'yes'"
                      @click="handleBulkRestore([row.id])"
                    >
                      <IconifyIconOnline
                        :icon="'ri:refresh-line'"
                        class="mr-2"
                      />
                      {{ $t("Restore") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="hasAuth('destroy-posts') && filterRef.isTrashed === 'yes'"
                      @click="handleBulkDestroy([row.id])"
                    >
                      <IconifyIconOnline
                        :icon="'ri:delete-bin-2-line'"
                        class="mr-2"
                      />
                      {{ $t("Destroy") }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>
    <!-- Post Drawer Form -->
    <PostDrawerForm
      ref="postFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          postFormRef?.resetForm();
          drawerValues = {};
        }
      "
    />

    <!-- Post Filter Form -->
    <PostFilterForm
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="() => { filterRef = { isTrashed: 'no' }; fnGetPosts(); }"
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
